import { z } from "zod";
import {
  Container,
  inject,
  clearNamespace,
  isRegistered,
  // 向后兼容的别名
  CInjection,
  VInjection,
} from "./src/ioc";

// 定义 schemas
const configSchema = z.object({
  apiUrl: z.string(),
  timeout: z.number(),
});

const loggerSchema = z.object({
  log: z.function(),
});

// 定义服务类
class Logger {
  log(message: string) {
    console.log(`[LOG] ${message}`);
  }
}

class ApiService {
  @inject(configSchema, "test")
  config: z.infer<typeof configSchema> = { apiUrl: "", timeout: 0 };

  @inject(loggerSchema, "test")
  logger: Logger | undefined;

  async fetchData() {
    this.logger?.log(`Fetching data from ${this.config.apiUrl}`);
    return "data";
  }
}

// 测试组件
function TestApp() {
  return (
    <Container namespace="test">
      {/* 使用新的函数名 */}
      <VInjection
        schema={configSchema}
        val={{ apiUrl: "https://api.example.com", timeout: 5000 }}
      />

      <CInjection schema={loggerSchema} ctor={Logger} />

      {/* 向后兼容的别名也应该工作 */}
      <VInjection schema={z.string()} val="test value" />

      <div>IoC Container Test</div>
    </Container>
  );
}

// 测试实用函数
function testUtilityFunctions() {
  console.log("=== 测试实用函数 ===");

  // 检查注册状态
  console.log("configSchema 是否已注册:", isRegistered(configSchema, "test"));
  console.log("loggerSchema 是否已注册:", isRegistered(loggerSchema, "test"));

  // 测试 ApiService
  const apiService = new ApiService();
  console.log("ApiService config:", apiService.config);
  console.log("ApiService logger:", apiService.logger);

  // 清理命名空间
  clearNamespace("test");
}

export { TestApp, testUtilityFunctions };
